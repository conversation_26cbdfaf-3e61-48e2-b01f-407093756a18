import { useRef, useState } from "react";
import { EditorState } from "../types";
import { Preview } from "./preview";
import { Toolbar } from "./toolbar";
import { Features } from "./features";
import { HowTo } from "./howto";
import { FAQ } from "./faq";
import { About } from "./about";
import { Testimonials } from "./testimonials";
import { CTA } from "./cta";
import { useRandomSound } from "../utils";

export function Home() {
  useRandomSound(0.001);

  const [state, setState] = useState<EditorState>({
    text: "Invincible",
    color: "#ebed00",
    showCredits: true,
    showWatermark: true,
    background: "url('/backgrounds/blue.jpg') no-repeat center center / cover",
    fontSize: 24,
    outline: 0,
    subtitleOffset: 0,
    outlineColor: "black",
    effect: null,
    generating: false,
    smallSubtitle: "BASED ON THE COMIC BOOK BY",
    subtitle: "<PERSON>, <PERSON>, & <PERSON>",
  });

  const canvasRef = useRef<HTMLDivElement>(null);

  return (
    <>
      {/* Hero Section */}
      <section className="bg-gradient-to-b from-slate-900 to-slate-950 py-12 md:py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-yellow-400 to-yellow-600 bg-clip-text text-transparent">
            Invincible Title Card Maker
          </h1>
          <p className="text-xl md:text-2xl text-slate-300 mb-8 max-w-5xl mx-auto">
            Design professional Invincible title card graphics with our free online editor. Customize text, colors, and backgrounds to create stunning title cards inspired by the hit animated series.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <a href="#editor" className="bg-yellow-500 hover:bg-yellow-600 text-black font-bold py-3 px-8 rounded-lg transition-colors">
              Start Creating
            </a>
            <a href="#faq" className="border border-yellow-500 text-yellow-500 hover:bg-yellow-500 hover:text-black font-bold py-3 px-8 rounded-lg transition-colors">
              View FAQ
            </a>
          </div>
        </div>
      </section>

      {/* Editor Section */}
      <main id="main-content" className="flex-1">
        <section id="editor" className="py-12 md:py-20">
          <div className="container mx-auto px-4">
            {/* 编辑器标题和描述 */}
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold mb-4 text-slate-100">
                Create Your Title Card
              </h2>
              <p className="text-lg text-slate-400 max-w-2xl mx-auto">
                Customize your Invincible title card with our intuitive editor.
                Adjust text, colors, backgrounds, and effects in real-time.
              </p>
            </div>

            {/* 主编辑器区域 */}
            <div className="grid lg:grid-cols-3 gap-8 max-w-7xl mx-auto items-start">
              {/* 预览区域 - 占据更大空间 */}
              <div className="lg:col-span-2 order-2 lg:order-1">
                <div className="sticky top-8">
                  <Preview canvasRef={canvasRef} state={state} />

                </div>
              </div>

              {/* 工具栏区域 */}
              <div className="lg:col-span-1 order-1 lg:order-2">
                <div className="sticky top-8">
                  <Toolbar canvasRef={canvasRef} state={state} setState={setState} />
                </div>
              </div>
            </div>
          </div>
        </section>
      </main>

      {/* Features Section */}
      <Features />

      {/* How To Use Section */}
      <HowTo />

      {/* FAQ Section */}
      <FAQ />

      {/* About Section */}
      <About />

      {/* Testimonials Section */}
      <Testimonials />

      {/* CTA Section */}
      <CTA />
    </>
  );
}
