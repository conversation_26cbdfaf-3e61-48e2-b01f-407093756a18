import { useEffect, useState, useRef, useCallback } from "react";
import { EditorState } from "../types";
import { effectPresets } from "./toolbar";
import Title from "./title";
import { Loading } from "./loading";

export function Preview(props: {
  state: EditorState;
  canvasRef: React.RefObject<HTMLDivElement | null>;
}) {
  const { state, canvasRef } = props;
  const [_canvasDimensions, setCanvasDimensions] = useState({
    width: 0,
    height: 0,
  });

  const audioRef = useRef<HTMLAudioElement | null>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const lastClickTimeRef = useRef<number>(0);

  // 防抖延迟（毫秒）
  const DEBOUNCE_DELAY = 300;

  const playAudio = useCallback(() => {
    const now = Date.now();

    // 防抖检查
    if (now - lastClickTimeRef.current < DEBOUNCE_DELAY) {
      return;
    }
    lastClickTimeRef.current = now;

    // 如果正在播放，先停止
    if (audioRef.current) {
      audioRef.current.pause();
      audioRef.current.currentTime = 0;
    }

    try {
      // 创建新的音频实例
      const audio = new Audio('/sounds/hmm.mp3');
      audioRef.current = audio;

      // 设置音频事件监听器
      audio.addEventListener('loadstart', () => setIsPlaying(true));
      audio.addEventListener('ended', () => setIsPlaying(false));
      audio.addEventListener('error', () => {
        console.error('音频加载失败');
        setIsPlaying(false);
      });
      audio.addEventListener('pause', () => setIsPlaying(false));

      // 播放音频
      audio.play().catch(error => {
        console.error('播放音频失败:', error);
        setIsPlaying(false);
      });
    } catch (error) {
      console.error('创建音频实例失败:', error);
      setIsPlaying(false);
    }
  }, []);

  // 组件卸载时清理音频
  useEffect(() => {
    return () => {
      if (audioRef.current) {
        audioRef.current.pause();
        audioRef.current = null;
      }
    };
  }, []);

  const canvasDimensions = state.generating
    ? {
        width: 1920,
        height: 1080,
      }
    : _canvasDimensions;

  useEffect(() => {
    const handleResize = () => {
      if (canvasRef.current) {
        setCanvasDimensions({
          width: canvasRef.current.clientWidth,
          height: canvasRef.current.clientHeight,
        });
      }
    };

    window.addEventListener("resize", handleResize);
    handleResize();

    return () => {
      window.removeEventListener("resize", handleResize);
    };
  }, [canvasRef]);

  return (
    <div className="bg-slate-900 rounded-xl overflow-hidden select-none relative shrink-0 border border-slate-700/50 w-full shadow-2xl shadow-slate-900/50 animate-fade-in-up" style={{ aspectRatio: '16/9' }}>
      {/* 语音播报按钮 */}
      <button
        onClick={playAudio}
        disabled={isPlaying}
        className={`absolute top-4 right-4 z-20 rounded-full p-3 transition-all duration-300 shadow-lg transform ${
          isPlaying
            ? 'bg-green-500 text-white cursor-not-allowed scale-110 shadow-green-500/30'
            : 'bg-yellow-500 hover:bg-yellow-600 text-black hover:scale-105 active:scale-95'
        }`}
        title={isPlaying ? "正在播放..." : "播放音频"}
      >
        {isPlaying ? (
          // 播放中的动画图标 - 使用音波效果
          <div className="relative w-5 h-5 flex items-center justify-center">
            <div className="absolute inset-0 rounded-full bg-white/20 animate-ping"></div>
            <svg
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="currentColor"
              className="w-4 h-4 relative z-10"
            >
              <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z"/>
            </svg>
          </div>
        ) : (
          // 默认扬声器图标
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="currentColor"
            className="w-5 h-5 transition-transform duration-200"
          >
            <path d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"/>
          </svg>
        )}
      </button>

      {state.generating && (
        <div className="absolute inset-0 bg-slate-900/95 backdrop-blur-sm flex items-center justify-center z-10">
          <Loading message="Generating..." size="lg" />
        </div>
      )}
      <div
        className="relative flex flex-col items-center justify-center gap-[5%]"
        ref={canvasRef}
        style={{
          width: !state.generating ? "100%" : "1920px",
          height: !state.generating ? "100%" : "1080px",
          background: state.background,
        }}
      >
        <Title
          text={state.text}
          color={state.color}
          fontSize={(canvasDimensions.width / 100) * state.fontSize}
          outlineColor={state.outlineColor}
          outline={state.outline}
          width={canvasDimensions.width}
        />
        {state.effect && (
          <div
            className="absolute inset-0 flex items-center justify-center"
            style={{
              opacity:
                effectPresets.find((e) => e.value === state.effect)?.opacity ||
                1,
              background: state.effect,
            }}
          />
        )}
        {state.showCredits && (
          <div
            style={{
              color: state.color,
              marginTop: `${state.subtitleOffset - 6}%`,
            }}
            className="text-center"
          >
            <div
              style={{
                fontSize: `${(canvasDimensions.width / 100) * 1.9}px`,
                fontWeight: canvasDimensions.width * 0.3,
              }}
            >
              {state.smallSubtitle}
            </div>
            <div
              style={{
                fontSize: `${(canvasDimensions.width / 100) * 3}px`,
                fontWeight: canvasDimensions.width * 0.3,
              }}
            >
              {state.subtitle}
            </div>
          </div>
        )}
        {state.showWatermark && (
          <div
            className="absolute bottom-0 right-0 text-white opacity-50 whitespace-nowrap"
            style={{
              fontSize: `${(canvasDimensions.width / 100) * 1.5}px`,
              padding: `${(canvasDimensions.width / 100) * 1}px`,
            }}
          >
            Made with invincibletitlecardmaker.com
          </div>
        )}
      </div>
    </div>
  );
}
